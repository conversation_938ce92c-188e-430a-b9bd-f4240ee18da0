# Playwright MCP 安装和使用指南

## 安装成功！

Microsoft Playwright MCP 已经成功安装在你的系统中。

## 安装的内容

- **@playwright/mcp**: 版本 0.0.26
- **Playwright**: 浏览器自动化库
- **相关依赖**: 所有必要的依赖包

## 配置文件

### 1. 基本 MCP 客户端配置 (mcp-client-config.json)
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest"
      ]
    }
  }
}
```

### 2. SSE 传输配置 (mcp-client-sse-config.json)
```json
{
  "mcpServers": {
    "playwright": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

### 3. Playwright MCP 服务器配置 (playwright-mcp-config.json)
包含浏览器设置、服务器端口、功能等配置。

## 使用方法

### 1. 启动 MCP 服务器
```bash
# 使用默认配置
npx @playwright/mcp

# 使用自定义配置文件
npx @playwright/mcp --config playwright-mcp-config.json

# 启动带端口的服务器（用于 SSE 传输）
npx @playwright/mcp --port 8931
```

### 2. 在不同客户端中配置

#### VS Code
```bash
code --add-mcp '{"name":"playwright","command":"npx","args":["@playwright/mcp@latest"]}'
```

#### Cursor
在 `Cursor Settings` -> `MCP` -> `Add new MCP Server` 中添加配置。

#### Claude Desktop
将配置添加到 Claude Desktop 的 MCP 配置文件中。

## 主要功能

- **浏览器自动化**: 点击、输入、导航等
- **页面快照**: 获取页面的可访问性快照
- **截图和 PDF**: 生成页面截图和 PDF
- **标签页管理**: 创建、切换、关闭标签页
- **网络监控**: 监控网络请求
- **文件上传**: 处理文件上传
- **测试生成**: 生成 Playwright 测试代码

## 当前状态

✅ Playwright MCP 服务器正在运行
✅ Chrome 浏览器已启动
✅ 配置文件已创建

## 下一步

1. 在你的 MCP 客户端（VS Code、Cursor、Claude Desktop 等）中配置 Playwright MCP
2. 开始使用浏览器自动化功能
3. 根据需要调整配置文件

## 故障排除

如果遇到模块依赖问题：
1. 清理 npm 缓存：`npm cache clean --force`
2. 重新安装：`npm install @playwright/mcp`
3. 使用本地安装而不是 npx

## 更多信息

查看官方文档：https://github.com/microsoft/playwright-mcp
